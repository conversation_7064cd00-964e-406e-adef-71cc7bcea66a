import React, { createContext, useContext, ReactNode } from 'react';

export interface Person {
  name: string;
  role: string;
}

export interface ProjectImage {
  url: string;
  photographer?: string;
}

export interface Project {
  id: number;
  title: string;
  tagline: string;
  imageUrl: string;
  description: string;
  people?: Person[];
  gallery?: (string | ProjectImage)[];
  photographer?: string;
}

// Sample project data
const projects: Project[] = [
  {
  id: 1,
  title: "De Exploderende Walvismusical",
  tagline: "",
  imageUrl: "/projects/de-exploderende-walvismusical/image.png",
  description: "De Explorende Walvismusical is een beeldende, vrolijk duistere en lichtelijk absurde muziektheatervoorstelling op locatie rond een levensechte ‘aangespoelde’ walvis. De voorstelling is geschikt voor iedereen vanaf 8 jaar en vertelt op poëtische en muzikale wijze hoe we als mens zó bang zijn om te falen en zó hard streven naar succes, dat we falen in het belangrijkste: onze soort in leven houden. De voorstelling is een parabel over de homo sapiens als bedreigde diersoort, met als grootste vijand zichzelf.\n\nIn een toekomstige wereld, op een brok land omringd door zee, vechten mensen al generaties lang tegen het water. Ze bouwen barrières, verbieden woorden die aan water herinneren, en houden rituelen om 'De Grote Nat' buiten te houden. Op een dag spoelt een gigantisch, naaktslakkig wezen aan op het strand. Het heeft vleugels en een adem die de grond doet trillen. Uit angst noemt men het ‘DROOG’ en het volk valt massaal voor het dier. Er ontstaat een ware walvisrage: alles krijgt een walvisthema – van T-shirts tot haargel.\n\nMaar niemand denkt eraan de walvis terug in zee te brengen. Behalve één jongen: het kind dat ooit zelf aanspoelde in een plastieken zak. Hij vraagt zich af of de walvis geen water nodig heeft. De walvis sterft. De lucht wordt zuur, de geur ondraaglijk. En dan, op een ochtend, ontploft de walvis – een explosie van vet vult de lucht.\n\nDeze voorstelling onderzoekt onze omgang met water, ecologie, en wat vreemd is. Ze is een ode aan de verbeelding en een waarschuwing voor onverschilligheid. Muzikaal wordt gebruik gemaakt van blaasinstrumenten, elektronische waterklanken en een koor van vrijwilligers. De tekst is als een sprookje, met een eigen taal en logica, die vervreemdt en tegelijk herkenbaar is. Wanneer de taal stokt, neemt de muziek het over. Het publiek wordt ondergedompeld in een zintuiglijke ervaring: door een koor in en rond het publiek lijkt de muziek overal, alsof men zich midden in de zee bevindt.\n\nDe voorstelling speelt zowel op locatie als in kleine zalen. De locatieversie draait rond een grote walvisfiguur en speelt zich af op plaatsen waar de afwezigheid van water voelbaar is: zandafgravingen, parkings, straten. De zaalversie is compacter en vereist voldoende technische mogelijkheden.",
  people: [
    { name: "Elsa van Dijk", role: "Concept & tekst" },
    { name: "Philippe Van de Velde", role: "Scenografie" },
    { name: "Joeri Cnapelinckx", role: "Spel & Muziek" },
    { name: "Ellis Meeusen", role: "Dramaturgie" },
    { name: "Arianne Koeleman", role: "Dramaturgie" },
    { name: "Jonas Van Thielen", role: "Spel" },
    { name: "Lies Vandeburie", role: "Spel" },
    { name: "Willeke de Boer", role: "Spel" },
    { name: "Elsa van Dijk", role: "Spel" },
    { name: "Esther de Koning", role: "Tekstcoaching" },
    { name: "Het Lab", role: "Co-productie" },
    { name: "Theatergroep De Horde", role: "Co-productie" },
    { name: "Lab Oost", role: "Co-productie" },
    { name: "Stad Leuven", role: "Steun" },
  ],
  gallery: [
    { url: "/projects/de-exploderende-walvismusical/image.png", },
    // "projects/de-exploderende-walvismusical/image"
  ],
},
  {
    id: 2,
    title: "Buffalo Bowling",
    tagline: "",
    imageUrl: "/projects/buffalo_bowling/horizontaal_pose.jpg",
    description: "1994. Kurt Cobain sterft, de allereerste PlayStation wordt gereleased, Pulp Fiction verovert de cinema’s en in een onooglijk Vlaams dorp worden vier meisjes geboren. 32 jaar later werken ze alle vier in dezelfde bowling langs de voorts volstrekt troosteloze steenweg die ze al heel hun leven hebben afgefietst; Bowling Buffalo. Ze zijn er ooit begonnen als jobstudent, maar ondertussen maken ze deel uit van het vaste meubilair. Ze ruiken nog steeds naar teen spirit en spelen nog altijd Crash Bandicoot terwijl ze over hun losse liefdesleven bakkeleien, milkshakes drinken en sigaretten roken. Tot één van hen haar vertrek aankondigt. Ze wil een nieuwe richting inslaan in haar leven en de bowling achter zich laten. Wat zich dan ontspint, is een verhaal over ambitie, trouw en het al dan niet najagen van verstofte dromen. Al die jaren hebben de vier vrouwen in een heerlijk gevoel van tijdloosheid geleefd, onder het gedempte licht van het retro bowlingdecor en hun eigen ongebreidelde fantasie. Maar nu iemand onaangekondigd het concept van ‘eindigheid’ heeft binnengesleurd, vallen alle kegels in recordtempo om.\n\nMet de steun van: Compagnie Cecilia, Theater Malpertuis, De Grote Post, ’t Werkhuys, Corso & De Klap",
    people: [
  {
    name: "Daan Borloo",
    role: "Concept&Tekst"
  },
  {
    name: "Lien Thys",
    role: "Spel"
  },
  {
    name: "Kiana Porte",
    role: "Spel"
  },
  {
    name: "Annelore Crollet",
    role: "Spel"
  },
  {
    name: "Hanne Timmermans",
    role: "Spel"
  },
  {
    name: "Joeri Cnapelinckx",
    role: "Muziek"
  },
  {
    name: "Ellis Meeusen",
    role: "Dramaturgie"
  },
  {
    name: "Ariane Koeleman",
    role: "Dramaturgie"
  }
],
    gallery: [
      { url: "/projects/buffalo_bowling/horizontaal_pose.jpg", photographer: "Koen Broos" },
      { url: "/projects/buffalo_bowling/horizontaal_actie.jpg", photographer: "Koen Broos" },
      { url: "/projects/buffalo_bowling/horizontaal_bar.jpg", photographer: "Koen Broos" }
    ],
    photographer: "Koen Broos"
  },
  {
    id: 3,
    title: "Steegje",
    tagline: "",
    imageUrl: "/uploads/temp.png",
    description: "",
    people: [{
      name: "Naam",
      role: "Rol"
    }],
    gallery: ["/uploads/temp.png"],
  },
  {
    id: 4,
    title: "Excuses voor het ongemak",
    tagline: "",
    imageUrl: "/projects/excuses_voor_het_ongemak/image.png",
    description: "Excuses voor het ongemak is een 4+ familie- en muziektheatervoorstelling die balanceert tussen poëzie, absurdisme en muzikaal kleuterraven. Gemaakt door een deel van het team achter SNØW (K.A.K.), biedt deze voorstelling een zintuiglijke ervaring over leven, sterven, schoonheid en walging, op het ritme van ieders levensbeat. Zonder woorden maar met veel tedem tedem’s, richt de voorstelling zich tot de allerkleinsten én hun entourage. Terwijl de poetsploeg afwezig is, komt de natuur het podium overnemen: van een verliefde raaf tot pissebedbroertjes, kakkerlakken, dode duiven en blinkende schimmel. Samen raven we het leven tegemoet, tot zelfs het kleinste geluid wordt versterkt door een ultragevoelige microfoon. Het stuk stelt vragen over de schoonheid en het ongemak van het bestaan, met als kern: waar leren grote mensen eigenlijk van wegkijken, en moeten we ons daarvoor excuseren?\n\nDe voorstelling werd wetenschappelijk onderbouwd via onderzoek met kinderen, en laat het jonge publiek zelf beslissen hoe ver ze durven kijken. Muzikaal, beeldend en fysiek theater zonder belerend vingertje maar mét veel ritme en verwondering.",
    people: [
    { name: "Annelore Crollet", role: "Concept & spel" },
    { name: "Nicolas Delalieux", role: "Concept & spel" },
    { name: "Nathalie Goossens", role: "Concept & spel" },
    { name: "Jeroen Stevens", role: "Muziek & spel" },
    { name: "Lucas Van Haesbroeck", role: "Scenografie & dramaturgie" },
    { name: "Tina Schott", role: "Poppen, kostuum, scenografie (i.s.m. FroeFroe)" }
    // choreografie volgt nog
  ],
    gallery: ["/projects/excuses_voor_het_ongemak/image.png"],
  }
];

interface ProjectContextType {
  projects: Project[];
  getProjectById: (id: number) => Project | undefined;
  getNextProject: (id: number) => Project | undefined;
  getPreviousProject: (id: number) => Project | undefined;
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export const ProjectProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const getProjectById = (id: number) => {
    return projects.find(project => project.id === id);
  };

  const getNextProject = (id: number) => {
    const currentIndex = projects.findIndex(project => project.id === id);
    if (currentIndex === -1 || currentIndex === projects.length - 1) {
      return undefined;
    }
    return projects[currentIndex + 1];
  };

  const getPreviousProject = (id: number) => {
    const currentIndex = projects.findIndex(project => project.id === id);
    if (currentIndex <= 0) {
      return undefined;
    }
    return projects[currentIndex - 1];
  };

  return (
    <ProjectContext.Provider value={{ projects, getProjectById, getNextProject, getPreviousProject }}>
      {children}
    </ProjectContext.Provider>
  );
};

export const useProjects = () => {
  const context = useContext(ProjectContext);
  if (context === undefined) {
    throw new Error('useProjects must be used within a ProjectProvider');
  }
  return context;
};
